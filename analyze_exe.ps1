# 分析exe文件的脚本
$exePath = ".\GSEEGB.exe"

Write-Host "分析文件: $exePath"
if (Test-Path $exePath) {
    Write-Host "文件大小: $((Get-Item $exePath).Length) 字节"

    # 读取文件的前1000字节来查找特征
    $bytes = [System.IO.File]::ReadAllBytes((Resolve-Path $exePath).Path)
} else {
    Write-Host "文件不存在: $exePath"
    exit
}
$header = $bytes[0..999]
$headerText = [System.Text.Encoding]::ASCII.GetString($header)

Write-Host "`n检查常见打包工具特征:"

# 检查PyInstaller
if ($headerText -match "PyInstaller" -or $headerText -match "_MEIPASS") {
    Write-Host "✓ 检测到PyInstaller打包"
}

# 检查UPX
if ($headerText -match "UPX") {
    Write-Host "✓ 检测到UPX压缩"
}

# 检查Python
if ($headerText -match "python" -or $headerText -match "Python") {
    Write-Host "✓ 检测到Python相关内容"
}

# 检查.NET
if ($headerText -match "\.NET" -or $headerText -match "mscoree") {
    Write-Host "✓ 检测到.NET程序"
}

# 检查Electron
if ($headerText -match "Electron" -or $headerText -match "node") {
    Write-Host "✓ 检测到Electron应用"
}

# 显示文件头部的十六进制
Write-Host "`n文件头部 (前32字节):"
$hexString = ""
for ($i = 0; $i -lt 32; $i++) {
    $hexString += "{0:X2} " -f $bytes[$i]
}
Write-Host $hexString

# 查找可读字符串
Write-Host "`n搜索可读字符串..."
$allText = [System.Text.Encoding]::ASCII.GetString($bytes)
$patterns = @("python", "PyInstaller", "UPX", "\.exe", "\.dll", "\.pyd", "import", "main")

foreach ($pattern in $patterns) {
    if ($allText -match $pattern) {
        Write-Host "找到模式: $pattern"
    }
}
