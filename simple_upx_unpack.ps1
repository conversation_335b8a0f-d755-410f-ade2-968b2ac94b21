# 简化版UPX解包脚本
param(
    [string]$ExeFile = "GSEEGB.exe"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "UPX解包工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# 检查文件是否存在
if (-not (Test-Path $ExeFile)) {
    Write-Host "错误: 文件 $ExeFile 不存在" -ForegroundColor Red
    exit 1
}

# 检查是否已有UPX
$upxPath = $null
if (Get-Command upx -ErrorAction SilentlyContinue) {
    $upxPath = "upx"
    Write-Host "✓ 找到系统UPX" -ForegroundColor Green
} elseif (Test-Path ".\upx.exe") {
    $upxPath = ".\upx.exe"
    Write-Host "✓ 找到本地UPX" -ForegroundColor Green
} else {
    Write-Host "正在下载UPX..." -ForegroundColor Yellow
    
    try {
        # 下载UPX
        $url = "https://github.com/upx/upx/releases/download/v4.2.2/upx-4.2.2-win64.zip"
        Invoke-WebRequest -Uri $url -OutFile "upx.zip"
        
        # 解压
        Expand-Archive -Path "upx.zip" -DestinationPath "upx_temp" -Force
        
        # 复制upx.exe
        $upxExe = Get-ChildItem -Path "upx_temp" -Filter "upx.exe" -Recurse | Select-Object -First 1
        if ($upxExe) {
            Copy-Item $upxExe.FullName ".\upx.exe"
            $upxPath = ".\upx.exe"
            Write-Host "✓ UPX下载完成" -ForegroundColor Green
        }
        
        # 清理
        Remove-Item "upx.zip" -Force -ErrorAction SilentlyContinue
        Remove-Item "upx_temp" -Recurse -Force -ErrorAction SilentlyContinue
    }
    catch {
        Write-Host "下载UPX失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

if (-not $upxPath) {
    Write-Host "无法获取UPX工具" -ForegroundColor Red
    exit 1
}

# 检查是否为UPX文件
Write-Host "检查文件格式..." -ForegroundColor Yellow
try {
    $testResult = & $upxPath -t $ExeFile 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "文件不是UPX压缩格式或已损坏" -ForegroundColor Red
        Write-Host "UPX输出: $testResult" -ForegroundColor Gray
        exit 1
    }
    Write-Host "✓ 确认为UPX压缩文件" -ForegroundColor Green
}
catch {
    Write-Host "检查文件时出错: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 生成输出文件名
$baseName = [System.IO.Path]::GetFileNameWithoutExtension($ExeFile)
$extension = [System.IO.Path]::GetExtension($ExeFile)
$outputFile = "${baseName}_unpacked${extension}"

# 复制文件
Copy-Item $ExeFile $outputFile
Write-Host "已复制文件到: $outputFile" -ForegroundColor Cyan

# 解包
Write-Host "正在解包..." -ForegroundColor Yellow
try {
    $unpackResult = & $upxPath -d $outputFile 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 解包成功!" -ForegroundColor Green
        
        # 显示文件大小
        $originalSize = (Get-Item $ExeFile).Length
        $unpackedSize = (Get-Item $outputFile).Length
        $ratio = [math]::Round((1 - $originalSize / $unpackedSize) * 100, 1)
        
        Write-Host "原始文件: $($originalSize.ToString('N0')) 字节" -ForegroundColor Cyan
        Write-Host "解包后: $($unpackedSize.ToString('N0')) 字节" -ForegroundColor Cyan
        Write-Host "压缩率: $ratio%" -ForegroundColor Cyan
        Write-Host "输出文件: $outputFile" -ForegroundColor Green
    } else {
        Write-Host "解包失败" -ForegroundColor Red
        Write-Host "错误: $unpackResult" -ForegroundColor Red
        Remove-Item $outputFile -Force -ErrorAction SilentlyContinue
    }
}
catch {
    Write-Host "解包时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
