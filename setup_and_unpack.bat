@echo off
chcp 65001 >nul
echo ========================================
echo UPX解包工具 - 自动安装和解包脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查是否已有UPX
where upx >nul 2>&1
if not errorlevel 1 (
    echo ✓ UPX已安装
    goto :unpack
)

:: 下载UPX
echo 正在下载UPX...
if not exist "upx" mkdir upx
cd upx

:: 下载UPX Windows版本
echo 下载UPX 4.2.2 for Windows...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/upx/upx/releases/download/v4.2.2/upx-4.2.2-win64.zip' -OutFile 'upx.zip'}"

if not exist "upx.zip" (
    echo 下载失败，请检查网络连接
    pause
    exit /b 1
)

:: 解压UPX
echo 正在解压UPX...
powershell -Command "& {Expand-Archive -Path 'upx.zip' -DestinationPath '.' -Force}"

:: 复制upx.exe到当前目录
if exist "upx-4.2.2-win64\upx.exe" (
    copy "upx-4.2.2-win64\upx.exe" "..\upx.exe"
    echo ✓ UPX安装完成
) else (
    echo 解压失败
    pause
    exit /b 1
)

cd ..

:unpack
echo.
echo 开始解包GSEEGB.exe...
python upx_unpacker.py GSEEGB.exe

echo.
echo 解包完成！
pause
