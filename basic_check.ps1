# 基本检查
$file = "GSEEGB.exe"

Write-Host "文件信息:"
Get-Item $file | Format-List Name, Length, CreationTime, LastWriteTime

Write-Host "检查文件头:"
$bytes = Get-Content $file -Encoding Byte -TotalCount 4
Write-Host "前4字节: $($bytes[0].ToString('X2')) $($bytes[1].ToString('X2')) $($bytes[2].ToString('X2')) $($bytes[3].ToString('X2'))"

if ($bytes[0] -eq 0x4D -and $bytes[1] -eq 0x5A) {
    Write-Host "这是一个PE文件"
}

Write-Host "`n尝试用7-Zip检查是否可以解压:"
if (Test-Path "C:\Program Files\7-Zip\7z.exe") {
    & "C:\Program Files\7-Zip\7z.exe" l $file
} elseif (Test-Path "C:\Program Files (x86)\7-Zip\7z.exe") {
    & "C:\Program Files (x86)\7-Zip\7z.exe" l $file
} else {
    Write-Host "未找到7-Zip"
}
