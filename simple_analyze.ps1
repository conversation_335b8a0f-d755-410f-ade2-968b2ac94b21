# 简单分析exe文件
$file = "GSEEGB.exe"

Write-Host "=== 文件基本信息 ==="
$fileInfo = Get-Item $file
Write-Host "文件名: $($fileInfo.Name)"
Write-Host "文件大小: $($fileInfo.Length) 字节 ($([math]::Round($fileInfo.Length/1MB, 2)) MB)"
Write-Host "创建时间: $($fileInfo.CreationTime)"
Write-Host "修改时间: $($fileInfo.LastWriteTime)"

Write-Host "`n=== 文件头部分析 ==="
# 读取前100字节
$stream = [System.IO.File]::OpenRead($file)
$buffer = New-Object byte[] 100
$stream.Read($buffer, 0, 100) | Out-Null
$stream.Close()

# 显示十六进制
Write-Host "前32字节 (十六进制):"
$hex = ""
for ($i = 0; $i -lt 32; $i++) {
    $hex += "{0:X2} " -f $buffer[$i]
    if (($i + 1) % 16 -eq 0) { $hex += "`n" }
}
Write-Host $hex

# 检查PE头
if ($buffer[0] -eq 0x4D -and $buffer[1] -eq 0x5A) {
    Write-Host "✓ 这是一个有效的PE文件 (Windows可执行文件)"
} else {
    Write-Host "✗ 不是标准的PE文件"
}

Write-Host "`n=== 尝试检测打包工具 ==="
# 使用findstr查找常见字符串
$patterns = @("python", "PyInstaller", "UPX", "_MEIPASS", "cx_Freeze", "Nuitka")

foreach ($pattern in $patterns) {
    try {
        $result = cmd /c "findstr /i /c:`"$pattern`" `"$file`" 2>nul"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 找到: $pattern"
        }
    } catch {
        # 忽略错误
    }
}

Write-Host "`n=== 建议的解包方法 ==="
Write-Host "1. 如果是PyInstaller打包: 使用 pyinstxtractor.py"
Write-Host "2. 如果是UPX压缩: 使用 upx -d GSEEGB.exe"
Write-Host "3. 如果是.NET程序: 使用 ILSpy 或 dnSpy"
Write-Host "4. 通用方法: 使用 7-Zip 尝试解压"
Write-Host "5. 高级分析: 使用 PE-bear, CFF Explorer, 或 Detect It Easy"
