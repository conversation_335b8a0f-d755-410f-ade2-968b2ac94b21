.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "UPX 1"
.TH UPX 1 2025-07-20 "upx 5.0.2" " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
upx \- compress or expand executable files
.SH SYNOPSIS
.IX Header "SYNOPSIS"
\&\fBupx\fR [\ \fIcommand\fR\ ] [\ \fIoptions\fR\ ] \fIfilename\fR...
.SH ABSTRACT
.IX Header "ABSTRACT"
.Vb 3
\&                    The Ultimate Packer for eXecutables
\&   Copyright (c) 1996\-2025 Markus Oberhumer, Laszlo Molnar & John Reiser
\&                           https://upx.github.io
.Ve
.PP
\&\fBUPX\fR is a portable, extendable, high-performance executable packer for
several different executable formats. It achieves an excellent compression
ratio and offers \fI*very*\fR fast decompression. Your executables suffer
no memory overhead or other drawbacks for most of the formats supported,
because of in-place decompression.
.SH DISCLAIMER
.IX Header "DISCLAIMER"
\&\fBUPX\fR comes with ABSOLUTELY NO WARRANTY; for details see the file COPYING.
.PP
Please report all problems or suggestions to the authors. Thanks.
.SH "SECURITY CONTEXT"
.IX Header "SECURITY CONTEXT"
IMPORTANT NOTE: \fBUPX\fR inherits the security context of any files it handles.
.PP
This means that packing, unpacking, or even testing or listing a file requires
the same security considerations as actually executing the file.
.PP
Use \fBUPX\fR on trusted files only!
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBUPX\fR is a versatile executable packer with the following features:
.PP
.Vb 3
\&  \- secure: as UPX is documented Open Source since many years any relevant
\&      Security/Antivirus software is able to peek inside UPX compressed
\&      apps to verify them
\&
\&  \- excellent compression ratio: typically compresses better than Zip,
\&      use UPX to decrease the size of your distribution !
\&
\&  \- very fast decompression: more than 500 MB/sec on any reasonably modern
\&      machine
\&
\&  \- no memory overhead for your compressed executables for most of the
\&      supported formats because of in\-place decompression
\&
\&  \- safe: you can list, test and unpack your executables.
\&      Also, a checksum of both the compressed and uncompressed file is
\&      maintained internally.
\&
\&  \- universal: UPX can pack a number of executable formats, including
\&      Windows programs and DLLs, macOS apps and Linux executables
\&
\&  \- portable: UPX is written in portable endian\-neutral C++
\&
\&  \- extendable: because of the class layout it\*(Aqs very easy to support
\&      new executable formats or add new compression algorithms
\&
\&  \- free: UPX is distributed with full source code under the GNU General
\&      Public License v2+, with special exceptions granting the free usage
\&      for commercial programs
.Ve
.PP
You probably understand now why we call \fBUPX\fR the "\fIultimate\fR"
executable packer.
.SH COMMANDS
.IX Header "COMMANDS"
.SS Compress
.IX Subsection "Compress"
This is the default operation, eg. \fBupx yourfile.exe\fR will compress the file
specified on the command line.
.SS Decompress
.IX Subsection "Decompress"
All \fBUPX\fR supported file formats can be unpacked using the \fB\-d\fR switch, eg.
\&\fBupx \-d yourfile.exe\fR will uncompress the file you've just compressed.
.SS Test
.IX Subsection "Test"
The \fB\-t\fR command tests the integrity of the compressed and uncompressed
data, eg. \fBupx \-t yourfile.exe\fR check whether your file can be safely
decompressed. Note, that this command doesn't check the whole file, only
the part that will be uncompressed during program execution. This means
that you should not use this command instead of a virus checker.
.SS List
.IX Subsection "List"
The \fB\-l\fR command prints out some information about the compressed files
specified on the command line as parameters, eg \fBupx \-l yourfile.exe\fR
shows the compressed / uncompressed size and the compression ratio of
\&\fIyourfile.exe\fR.
.SH OPTIONS
.IX Header "OPTIONS"
\&\fB\-q\fR: be quiet, suppress warnings
.PP
\&\fB\-q \-q\fR (or \fB\-qq\fR): be very quiet, suppress errors
.PP
\&\fB\-q \-q \-q\fR (or \fB\-qqq\fR): produce no output at all
.PP
\&\fB\-\-help\fR: prints the help
.PP
\&\fB\-\-version\fR: print the version of \fBUPX\fR
.PP
\&\fB\-\-exact\fR: when compressing, require to be able to get a byte-identical file
after decompression with option \fB\-d\fR. [NOTE: this is work in progress and is
not supported for all formats yet. If you do care, as a workaround you can
compress and then decompress your program a first time \- any further
compress-decompress steps should then yield byte-identical results
as compared to the first decompressed version.]
.PP
\&\fB\-k\fR: keep backup files
.PP
\&\fB\-o file\fR: write output to file
.PP
[ ...more docs need to be written... \- type `\fBupx \-\-help\fR' for now ]
.SH "COMPRESSION LEVELS & TUNING"
.IX Header "COMPRESSION LEVELS & TUNING"
\&\fBUPX\fR offers ten different compression levels from \fB\-1\fR to \fB\-9\fR,
and \fB\-\-best\fR.  The default compression level is \fB\-8\fR for files
smaller than 512 KiB, and \fB\-7\fR otherwise.
.IP \(bu 4
Compression levels 1, 2 and 3 are pretty fast.
.IP \(bu 4
Compression levels 4, 5 and 6 achieve a good time/ratio performance.
.IP \(bu 4
Compression levels 7, 8 and 9 favor compression ratio over speed.
.IP \(bu 4
Compression level \fB\-\-best\fR may take a long time.
.PP
Note that compression level \fB\-\-best\fR can be somewhat slow for large
files, but you definitely should use it when releasing a final version
of your program.
.PP
Quick info for achieving the best compression ratio:
.IP \(bu 4
Try \fBupx \-\-brute \-\-no\-lzma myfile.exe\fR or even
\&\fBupx \-\-ultra\-brute \-\-no\-lzma myfile.exe\fR.
.IP \(bu 4
The option \fB\-\-lzma\fR enables LZMA compression, which compresses better but
is *significantly slower* at decompression. You probably do not want
to use it for large files.
.Sp
(Note that \fB\-\-lzma\fR is automatically enabled by \fB\-\-all\-methods\fR and
\&\fB\-\-brute\fR, use \fB\-\-no\-lzma\fR to override.)
.IP \(bu 4
Try if \fB\-\-overlay=strip\fR works.
.IP \(bu 4
For win32/pe programs there's \fB\-\-strip\-relocs=0\fR. See notes below.
.SH "OVERLAY HANDLING OPTIONS"
.IX Header "OVERLAY HANDLING OPTIONS"
Info: An "overlay" means auxiliary data attached after the logical end of
an executable, and it often contains application specific data
(this is a common practice to avoid an extra data file, though
it would be better to use resource sections).
.PP
\&\fBUPX\fR handles overlays like many other executable packers do: it simply
copies the overlay after the compressed image. This works with some
files, but doesn't work with others, depending on how an application
actually accesses this overlaid data.
.PP
.Vb 1
\&  \-\-overlay=copy    Copy any extra data attached to the file. [DEFAULT]
\&
\&  \-\-overlay=strip   Strip any overlay from the program instead of
\&                    copying it. Be warned, this may make the compressed
\&                    program crash or otherwise unusable.
\&
\&  \-\-overlay=skip    Refuse to compress any program which has an overlay.
.Ve
.SH "ENVIRONMENT VARIABLE"
.IX Header "ENVIRONMENT VARIABLE"
The environment variable \fBUPX\fR can hold a set of default
options for \fBUPX\fR. These options are interpreted first and
can be overwritten by explicit command line parameters.
For example:
.PP
.Vb 3
\&    for DOS/Windows:   set UPX=\-9 \-\-compress\-icons#0
\&    for sh/ksh/zsh:    UPX="\-9 \-\-compress\-icons=0"; export UPX
\&    for csh/tcsh:      setenv UPX "\-9 \-\-compress\-icons=0"
.Ve
.PP
Under DOS/Windows you must use '#' instead of '=' when setting the
environment variable because of a COMMAND.COM limitation.
.PP
Not all of the options are valid in the environment variable \-
\&\fBUPX\fR will tell you.
.PP
You can explicitly use the \fB\-\-no\-env\fR option to ignore the
environment variable.
.SH "NOTES FOR THE SUPPORTED EXECUTABLE FORMATS"
.IX Header "NOTES FOR THE SUPPORTED EXECUTABLE FORMATS"
.SS "NOTES FOR ATARI/TOS"
.IX Subsection "NOTES FOR ATARI/TOS"
This is the executable format used by the Atari ST/TT, a Motorola 68000
based personal computer which was popular in the late '80s. Support
of this format is only because of nostalgic feelings of one of
the authors and serves no practical purpose :\-).
See https://freemint.github.io for more info.
.PP
Packed programs will be byte-identical to the original after uncompression.
All debug information will be stripped, though.
.PP
Extra options available for this executable format:
.PP
.Vb 4
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
.Ve
.SS "NOTES FOR BVMLINUZ/I386"
.IX Subsection "NOTES FOR BVMLINUZ/I386"
Same as vmlinuz/i386.
.SS "NOTES FOR DOS/COM"
.IX Subsection "NOTES FOR DOS/COM"
Obviously \fBUPX\fR won't work with executables that want to read data from
themselves (like some commandline utilities that ship with Win95/98/ME).
.PP
Compressed programs only work on a 286+.
.PP
Packed programs will be byte-identical to the original after uncompression.
.PP
Maximum uncompressed size: ~65100 bytes.
.PP
Extra options available for this executable format:
.PP
.Vb 1
\&  \-\-8086              Create an executable that works on any 8086 CPU.
\&
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-all\-filters       Compress the program several times, using all
\&                      available preprocessing filters. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default filter gives the best results anyway.
.Ve
.SS "NOTES FOR DOS/EXE"
.IX Subsection "NOTES FOR DOS/EXE"
dos/exe stands for all "normal" 16\-bit DOS executables.
.PP
Obviously \fBUPX\fR won't work with executables that want to read data from
themselves (like some command line utilities that ship with Win95/98/ME).
.PP
Compressed programs only work on a 286+.
.PP
Extra options available for this executable format:
.PP
.Vb 1
\&  \-\-8086              Create an executable that works on any 8086 CPU.
\&
\&  \-\-no\-reloc          Use no relocation records in the exe header.
\&
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
.Ve
.SS "NOTES FOR DOS/SYS"
.IX Subsection "NOTES FOR DOS/SYS"
Compressed programs only work on a 286+.
.PP
Packed programs will be byte-identical to the original after uncompression.
.PP
Maximum uncompressed size: ~65350 bytes.
.PP
Extra options available for this executable format:
.PP
.Vb 1
\&  \-\-8086              Create an executable that works on any 8086 CPU.
\&
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-all\-filters       Compress the program several times, using all
\&                      available preprocessing filters. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default filter gives the best results anyway.
.Ve
.SS "NOTES FOR DJGPP2/COFF"
.IX Subsection "NOTES FOR DJGPP2/COFF"
First of all, it is recommended to use \fBUPX\fR *instead* of \fBstrip\fR. strip has
the very bad habit of replacing your stub with its own (outdated) version.
Additionally \fBUPX\fR corrects a bug/feature in strip v2.8.x: it
will fix the 4 KiB alignment of the stub.
.PP
\&\fBUPX\fR includes the full functionality of stubify. This means it will
automatically stubify your COFF files. Use the option \fB\-\-coff\fR to
disable this functionality (see below).
.PP
\&\fBUPX\fR automatically handles Allegro packfiles.
.PP
The DLM format (a rather exotic shared library extension) is not supported.
.PP
Packed programs will be byte-identical to the original after uncompression.
All debug information and trailing garbage will be stripped, though.
.PP
Extra options available for this executable format:
.PP
.Vb 2
\&  \-\-coff              Produce COFF output instead of EXE. By default
\&                      UPX keeps your current stub.
\&
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-all\-filters       Compress the program several times, using all
\&                      available preprocessing filters. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default filter gives the best results anyway.
.Ve
.SS "NOTES FOR LINUX [general]"
.IX Subsection "NOTES FOR LINUX [general]"
Introduction
.PP
.Vb 4
\&  Linux/386 support in UPX consists of 3 different executable formats,
\&  one optimized for ELF executables ("linux/elf386"), one optimized
\&  for shell scripts ("linux/sh386"), and one generic format
\&  ("linux/386").
\&
\&  We will start with a general discussion first, but please
\&  also read the relevant docs for each of the individual formats.
\&
\&  Also, there is special support for bootable kernels \- see the
\&  description of the vmlinuz/386 format.
.Ve
.PP
General user's overview
.PP
.Vb 7
\&  Running a compressed executable program trades less space on a
\&  \`\`permanent\*(Aq\*(Aq storage medium (such as a hard disk, floppy disk,
\&  CD\-ROM, flash memory, EPROM, etc.) for more space in one or more
\&  \`\`temporary\*(Aq\*(Aq storage media (such as RAM, swap space, /tmp, etc.).
\&  Running a compressed executable also requires some additional CPU
\&  cycles to generate the compressed executable in the first place,
\&  and to decompress it at each invocation.
\&
\&  How much space is traded?  It depends on the executable, but many
\&  programs save 30% to 50% of permanent disk space.  How much CPU
\&  overhead is there?  Again, it depends on the executable, but
\&  decompression speed generally is at least many megabytes per second,
\&  and frequently is limited by the speed of the underlying disk
\&  or network I/O.
\&
\&  Depending on the statistics of usage and access, and the relative
\&  speeds of CPU, RAM, swap space, /tmp, and file system storage, then
\&  invoking and running a compressed executable can be faster than
\&  directly running the corresponding uncompressed program.
\&  The operating system might perform fewer expensive I/O operations
\&  to invoke the compressed program.  Paging to or from swap space
\&  or /tmp might be faster than paging from the general file system.
\&  \`\`Medium\-sized\*(Aq\*(Aq programs which access about 1/3 to 1/2 of their
\&  stored program bytes can do particularly well with compression.
\&  Small programs tend not to benefit as much because the absolute
\&  savings is less.  Big programs tend not to benefit proportionally
\&  because each invocation may use only a small fraction of the program,
\&  yet UPX decompresses the entire program before invoking it.
\&  But in environments where disk or flash memory storage is limited,
\&  then compression may win anyway.
\&
\&  Currently, executables compressed by UPX do not share RAM at runtime
\&  in the way that executables mapped from a file system do.  As a
\&  result, if the same program is run simultaneously by more than one
\&  process, then using the compressed version will require more RAM and/or
\&  swap space.  So, shell programs (bash, csh, etc.)  and \`\`make\*(Aq\*(Aq
\&  might not be good candidates for compression.
\&
\&  UPX recognizes three executable formats for Linux: Linux/elf386,
\&  Linux/sh386, and Linux/386.  Linux/386 is the most generic format;
\&  it accommodates any file that can be executed.  At runtime, the UPX
\&  decompression stub re\-creates in /tmp a copy of the original file,
\&  and then the copy is (re\-)executed with the same arguments.
\&  ELF binary executables prefer the Linux/elf386 format by default,
\&  because UPX decompresses them directly into RAM, uses only one
\&  exec, does not use space in /tmp, and does not use /proc.
\&  Shell scripts where the underlying shell accepts a \`\`\-c\*(Aq\*(Aq argument
\&  can use the Linux/sh386 format.  UPX decompresses the shell script
\&  into low memory, then maps the shell and passes the entire text of the
\&  script as an argument with a leading \`\`\-c\*(Aq\*(Aq.
.Ve
.PP
General benefits:
.PP
.Vb 4
\&  \- UPX can compress all executables, be it AOUT, ELF, libc4, libc5,
\&    libc6, Shell/Perl/Python/... scripts, standalone Java .class
\&    binaries, or whatever...
\&    All scripts and programs will work just as before.
\&
\&  \- Compressed programs are completely self\-contained. No need for
\&    any external program.
\&
\&  \- UPX keeps your original program untouched. This means that
\&    after decompression you will have a byte\-identical version,
\&    and you can use UPX as a file compressor just like gzip.
\&    [ Note that UPX maintains a checksum of the file internally,
\&      so it is indeed a reliable alternative. ]
\&
\&  \- As the stub only uses syscalls and isn\*(Aqt linked against libc it
\&    should run under any Linux configuration that can run ELF
\&    binaries.
\&
\&  \- For the same reason compressed executables should run under
\&    FreeBSD and other systems which can run Linux binaries.
\&    [ Please send feedback on this topic ]
.Ve
.PP
General drawbacks:
.PP
.Vb 4
\&  \- It is not advisable to compress programs which usually have many
\&    instances running (like \`sh\*(Aq or \`make\*(Aq) because the common segments of
\&    compressed programs won\*(Aqt be shared any longer between different
\&    processes.
\&
\&  \- \`ldd\*(Aq and \`size\*(Aq won\*(Aqt show anything useful because all they
\&    see is the statically linked stub.  Since version 0.82 the section
\&    headers are stripped from the UPX stub and \`size\*(Aq doesn\*(Aqt even
\&    recognize the file format.  The file patches/patch\-elfcode.h has a
\&    patch to fix this bug in \`size\*(Aq and other programs which use GNU BFD.
.Ve
.PP
General notes:
.PP
.Vb 2
\&  \- As UPX leaves your original program untouched it is advantageous
\&    to strip it before compression.
\&
\&  \- If you compress a script you will lose platform independence \-
\&    this could be a problem if you are using NFS mounted disks.
\&
\&  \- Compression of suid, guid and sticky\-bit programs is rejected
\&    because of possible security implications.
\&
\&  \- For the same reason there is no sense in making any compressed
\&    program suid.
\&
\&  \- Obviously UPX won\*(Aqt work with executables that want to read data
\&    from themselves. E.g., this might be a problem for Perl scripts
\&    which access their _\|_DATA_\|_ lines.
\&
\&  \- In case of internal errors the stub will abort with exitcode 127.
\&    Typical reasons for this to happen are that the program has somehow
\&    been modified after compression.
\&    Running \`strace \-o strace.log compressed_file\*(Aq will tell you more.
.Ve
.SS "NOTES FOR LINUX/ELF386"
.IX Subsection "NOTES FOR LINUX/ELF386"
Please read the general Linux description first.
.PP
The linux/elf386 format decompresses directly into RAM,
uses only one exec, does not use space in /tmp,
and does not use /proc.
.PP
Linux/elf386 is automatically selected for Linux ELF executables.
.PP
Packed programs will be byte-identical to the original after uncompression.
.PP
How it works:
.PP
.Vb 9
\&  For ELF executables, UPX decompresses directly to memory, simulating
\&  the mapping that the operating system kernel uses during exec(),
\&  including the PT_INTERP program interpreter (if any).
\&  The brk() is set by a special PT_LOAD segment in the compressed
\&  executable itself.  UPX then wipes the stack clean except for
\&  arguments, environment variables, and Elf_auxv entries (this is
\&  required by bugs in the startup code of /lib/ld\-linux.so as of
\&  May 2000), and transfers control to the program interpreter or
\&  the e_entry address of the original executable.
\&
\&  The UPX stub is about 1700 bytes long, partly written in assembler
\&  and only uses kernel syscalls. It is not linked against any libc.
.Ve
.PP
Specific drawbacks:
.PP
.Vb 9
\&  \- For linux/elf386 and linux/sh386 formats, you will be relying on
\&    RAM and swap space to hold all of the decompressed program during
\&    the lifetime of the process.  If you already use most of your swap
\&    space, then you may run out.  A system that is "out of memory"
\&    can become fragile.  Many programs do not react gracefully when
\&    malloc() returns 0.  With newer Linux kernels, the kernel
\&    may decide to kill some processes to regain memory, and you
\&    may not like the kernel\*(Aqs choice of which to kill.  Running
\&    /usr/bin/top is one way to check on the usage of swap space.
.Ve
.PP
Extra options available for this executable format:
.PP
.Vb 1
\&  (none)
.Ve
.SS "NOTES FOR LINUX/SH386"
.IX Subsection "NOTES FOR LINUX/SH386"
Please read the general Linux description first.
.PP
Shell scripts where the underling shell accepts a ``\-c'' argument
can use the Linux/sh386 format.  \fBUPX\fR decompresses the shell script
into low memory, then maps the shell and passes the entire text of the
script as an argument with a leading ``\-c''.
It does not use space in /tmp, and does not use /proc.
.PP
Linux/sh386 is automatically selected for shell scripts that
use a known shell.
.PP
Packed programs will be byte-identical to the original after uncompression.
.PP
How it works:
.PP
.Vb 8
\&  For shell script executables (files beginning with "#!/" or "#! /")
\&  where the shell is known to accept "\-c <command>", UPX decompresses
\&  the file into low memory, then maps the shell (and its PT_INTERP),
\&  and passes control to the shell with the entire decompressed file
\&  as the argument after "\-c".  Known shells are sh, ash, bash, bsh, csh,
\&  ksh, tcsh, pdksh.  Restriction: UPX cannot use this method
\&  for shell scripts which use the one optional string argument after
\&  the shell name in the script (example: "#! /bin/sh option3\en".)
\&
\&  The UPX stub is about 1700 bytes long, partly written in assembler
\&  and only uses kernel syscalls. It is not linked against any libc.
.Ve
.PP
Specific drawbacks:
.PP
.Vb 9
\&  \- For linux/elf386 and linux/sh386 formats, you will be relying on
\&    RAM and swap space to hold all of the decompressed program during
\&    the lifetime of the process.  If you already use most of your swap
\&    space, then you may run out.  A system that is "out of memory"
\&    can become fragile.  Many programs do not react gracefully when
\&    malloc() returns 0.  With newer Linux kernels, the kernel
\&    may decide to kill some processes to regain memory, and you
\&    may not like the kernel\*(Aqs choice of which to kill.  Running
\&    /usr/bin/top is one way to check on the usage of swap space.
.Ve
.PP
Extra options available for this executable format:
.PP
.Vb 1
\&  (none)
.Ve
.SS "NOTES FOR LINUX/386"
.IX Subsection "NOTES FOR LINUX/386"
Please read the general Linux description first.
.PP
The generic linux/386 format decompresses to /tmp and needs
/proc file system support. It starts the decompressed program
via the \fBexecve()\fR syscall.
.PP
Linux/386 is only selected if the specialized linux/elf386
and linux/sh386 won't recognize a file.
.PP
Packed programs will be byte-identical to the original after uncompression.
.PP
How it works:
.PP
.Vb 7
\&  For files which are not ELF and not a script for a known "\-c" shell,
\&  UPX uses kernel execve(), which first requires decompressing to a
\&  temporary file in the file system.  Interestingly \-
\&  because of the good memory management of the Linux kernel \- this
\&  often does not introduce a noticeable delay, and in fact there
\&  will be no disk access at all if you have enough free memory as
\&  the entire process takes places within the file system buffers.
\&
\&  A compressed executable consists of the UPX stub and an overlay
\&  which contains the original program in a compressed form.
\&
\&  The UPX stub is a statically linked ELF executable and does
\&  the following at program startup:
\&
\&    1) decompress the overlay to a temporary location in /tmp
\&    2) open the temporary file for reading
\&    3) try to delete the temporary file and start (execve)
\&       the uncompressed program in /tmp using /proc/<pid>/fd/X as
\&       attained by step 2)
\&    4) if that fails, fork off a subprocess to clean up and
\&       start the program in /tmp in the meantime
\&
\&  The UPX stub is about 1700 bytes long, partly written in assembler
\&  and only uses kernel syscalls. It is not linked against any libc.
.Ve
.PP
Specific drawbacks:
.PP
.Vb 4
\&  \- You need additional free disk space for the uncompressed program
\&    in your /tmp directory. This program is deleted immediately after
\&    decompression, but you still need it for the full execution time
\&    of the program.
\&
\&  \- You must have /proc file system support as the stub wants to open
\&    /proc/<pid>/exe and needs /proc/<pid>/fd/X. This also means that you
\&    cannot compress programs that are used during the boot sequence
\&    before /proc is mounted.
\&
\&  \- Utilities like \`top\*(Aq will display numerical values in the process
\&    name field. This is because Linux computes the process name from
\&    the first argument of the last execve syscall (which is typically
\&    something like /proc/<pid>/fd/3).
\&
\&  \- Because of temporary decompression to disk the decompression speed
\&    is not as fast as with the other executable formats. Still, I can see
\&    no noticeable delay when starting programs like my ~3 MiB emacs (which
\&    is less than 1 MiB when compressed :\-).
.Ve
.PP
Extra options available for this executable format:
.PP
.Vb 3
\&  \-\-force\-execve      Force the use of the generic linux/386 "execve"
\&                      format, i.e. do not try the linux/elf386 and
\&                      linux/sh386 formats.
.Ve
.SS "NOTES FOR PS1/EXE"
.IX Subsection "NOTES FOR PS1/EXE"
This is the executable format used by the Sony PlayStation (PSone),
a MIPS R3000 based gaming console which is popular since the late '90s.
Support of this format is very similar to the Atari one, because of
nostalgic feelings of one of the authors.
.PP
Packed programs will be byte-identical to the original after uncompression,
until further notice.
.PP
Maximum uncompressed size: ~1.89 / ~7.60 MiB.
.PP
Notes:
.PP
.Vb 6
\&  \- UPX creates as default a suitable executable for CD\-Mastering
\&    and console transfer. For a CD\-Master main executable you could also try
\&    the special option "\-\-boot\-only" as described below.
\&    It has been reported that upx packed executables are fully compatible with
\&    the Sony PlayStation 2 (PS2, PStwo) and Sony PlayStation Portable (PSP) in
\&    Sony PlayStation (PSone) emulation mode.
\&
\&  \- Normally the packed files use the same memory areas like the uncompressed
\&    versions, so they will not override other memory areas while unpacking.
\&    If this isn\*(Aqt possible UPX will abort showing a \*(Aqpacked data overlap\*(Aq
\&    error. With the "\-\-force" option UPX will relocate the loading address
\&    for the packed file, but this isn\*(Aqt a real problem if it is a single or
\&    the main executable.
.Ve
.PP
Extra options available for this executable format:
.PP
.Vb 4
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-8\-bit             Uses 8 bit size compression [default: 32 bit]
\&
\&  \-\-8mib\-ram          PSone has 8 MiB ram available [default: 2 MiB]
\&
\&  \-\-boot\-only         This format is for main exes and CD\-Mastering only !
\&                      It may slightly improve the compression ratio,
\&                      decompression routines are faster than default ones.
\&                      But it cannot be used for console transfer !
\&
\&  \-\-no\-align          This option disables CD mode 2 data sector format
\&                      alignment. May slightly improves the compression ratio,
\&                      but the compressed executable will not boot from a CD.
\&                      Use it for console transfer only !
.Ve
.SS "NOTES FOR RTM32/PE and ARM/PE"
.IX Subsection "NOTES FOR RTM32/PE and ARM/PE"
Same as win32/pe.
.SS "NOTES FOR TMT/ADAM"
.IX Subsection "NOTES FOR TMT/ADAM"
This format is used by the TMT Pascal compiler \- see http://www.tmt.com/ .
.PP
Extra options available for this executable format:
.PP
.Vb 4
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-all\-filters       Compress the program several times, using all
\&                      available preprocessing filters. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default filter gives the best results anyway.
.Ve
.SS "NOTES FOR VMLINUZ/386"
.IX Subsection "NOTES FOR VMLINUZ/386"
The vmlinuz/386 and bvmlinuz/386 formats take a gzip-compressed
bootable Linux kernel image ("vmlinuz", "zImage", "bzImage"),
gzip-decompress it and re-compress it with the \fBUPX\fR compression method.
.PP
vmlinuz/386 is completely unrelated to the other Linux executable
formats, and it does not share any of their drawbacks.
.PP
Notes:
.PP
.Vb 3
\&  \- Be sure that "vmlinuz/386" or "bvmlinuz/386" is displayed
\&  during compression \- otherwise a wrong executable format
\&  may have been used, and the kernel won\*(Aqt boot.
.Ve
.PP
Benefits:
.PP
.Vb 4
\&  \- Better compression (but note that the kernel was already compressed,
\&  so the improvement is not as large as with other formats).
\&  Still, the bytes saved may be essential for special needs like
\&  boot disks.
\&
\&     For example, this is what I get for my 2.2.16 kernel:
\&        1589708  vmlinux
\&         641073  bzImage        [original]
\&         560755  bzImage.upx    [compressed by "upx \-9"]
\&
\&  \- Much faster decompression at kernel boot time (but kernel
\&    decompression speed is not really an issue these days).
.Ve
.PP
Drawbacks:
.PP
.Vb 1
\&  (none)
.Ve
.PP
Extra options available for this executable format:
.PP
.Vb 4
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-all\-filters       Compress the program several times, using all
\&                      available preprocessing filters. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default filter gives the best results anyway.
.Ve
.SS "NOTES FOR WATCOM/LE"
.IX Subsection "NOTES FOR WATCOM/LE"
\&\fBUPX\fR has been successfully tested with the following extenders:
  DOS4G, DOS4GW, PMODE/W, DOS32a, CauseWay.
  The WDOS/X extender is partly supported (for details
  see the file bugs BUGS).
.PP
DLLs and the LX format are not supported.
.PP
Extra options available for this executable format:
.PP
.Vb 2
\&  \-\-le                Produce an unbound LE output instead of
\&                      keeping the current stub.
.Ve
.SS "NOTES FOR WIN32/PE"
.IX Subsection "NOTES FOR WIN32/PE"
The PE support in \fBUPX\fR is quite stable now, but probably there are
still some incompatibilities with some files.
.PP
Because of the way \fBUPX\fR (and other packers for this format) works, you
can see increased memory usage of your compressed files because the whole
program is loaded into memory at startup.
If you start several instances of huge compressed programs you're
wasting memory because the common segments of the program won't
get shared across the instances.
On the other hand if you're compressing only smaller programs, or
running only one instance of larger programs, then this penalty is
smaller, but it's still there.
.PP
If you're running executables from network, then compressed programs
will load faster, and require less bandwidth during execution.
.PP
DLLs are supported. But UPX compressed DLLs can not share common data and
code when they got used by multiple applications. So compressing msvcrt.dll
is a waste of memory, but compressing the dll plugins of a particular
application may be a better idea.
.PP
Screensavers are supported, with the restriction that the filename
must end with ".scr" (as screensavers are handled slightly different
than normal exe files).
.PP
UPX compressed PE files have some minor memory overhead (usually in the
10 \- 30 KiB range) which can be seen by specifying the "\-i" command
line switch during compression.
.PP
Extra options available for this executable format:
.PP
.Vb 9
\& \-\-compress\-exports=0 Don\*(Aqt compress the export section.
\&                      Use this if you plan to run the compressed
\&                      program under Wine.
\& \-\-compress\-exports=1 Compress the export section. [DEFAULT]
\&                      Compression of the export section can improve the
\&                      compression ratio quite a bit but may not work
\&                      with all programs (like winword.exe).
\&                      UPX never compresses the export section of a DLL
\&                      regardless of this option.
\&
\&  \-\-compress\-icons=0  Don\*(Aqt compress any icons.
\&  \-\-compress\-icons=1  Compress all but the first icon.
\&  \-\-compress\-icons=2  Compress all icons which are not in the
\&                      first icon directory. [DEFAULT]
\&  \-\-compress\-icons=3  Compress all icons.
\&
\&  \-\-compress\-resources=0  Don\*(Aqt compress any resources at all.
\&
\&  \-\-keep\-resource=list Don\*(Aqt compress resources specified by the list.
\&                      The members of the list are separated by commas.
\&                      A list member has the following format: I<type[/name]>.
\&                      I<Type> is the type of the resource. Standard types
\&                      must be specified as decimal numbers, user types can be
\&                      specified by decimal IDs or strings. I<Name> is the
\&                      identifier of the resource. It can be a decimal number
\&                      or a string. For example:
\&
\&                      \-\-keep\-resource=2/MYBITMAP,5,6/12345
\&
\&                      UPX won\*(Aqt compress the named bitmap resource "MYBITMAP",
\&                      it leaves every dialog (5) resource uncompressed, and
\&                      it won\*(Aqt touch the string table resource with identifier
\&                      12345.
\&
\&  \-\-force             Force compression even when there is an
\&                      unexpected value in a header field.
\&                      Use with care.
\&
\&  \-\-strip\-relocs=0    Don\*(Aqt strip relocation records.
\&  \-\-strip\-relocs=1    Strip relocation records. [DEFAULT]
\&                      This option only works on executables with base
\&                      address greater or equal to 0x400000. Usually the
\&                      compressed files becomes smaller, but some files
\&                      may become larger. Note that the resulting file will
\&                      not work under Windows 3.x (Win32s).
\&                      UPX never strips relocations from a DLL
\&                      regardless of this option.
\&
\&  \-\-all\-methods       Compress the program several times, using all
\&                      available compression methods. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default method gives the best results anyway.
\&
\&  \-\-all\-filters       Compress the program several times, using all
\&                      available preprocessing filters. This may improve
\&                      the compression ratio in some cases, but usually
\&                      the default filter gives the best results anyway.
.Ve
.SH DIAGNOSTICS
.IX Header "DIAGNOSTICS"
Exit status is normally 0; if an error occurs, exit status
is 1. If a warning occurs, exit status is 2.
.PP
\&\fBUPX\fR's diagnostics are intended to be self-explanatory.
.SH BUGS
.IX Header "BUGS"
Please report all bugs immediately to the authors.
.SH AUTHORS
.IX Header "AUTHORS"
.Vb 2
\& Markus F.X.J. Oberhumer <<EMAIL>>
\& http://www.oberhumer.com
\&
\& Laszlo Molnar <<EMAIL>>
\&
\& John F. Reiser <<EMAIL>>
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright (C) 1996\-2025 Markus Franz Xaver Johannes Oberhumer
.PP
Copyright (C) 1996\-2025 Laszlo Molnar
.PP
Copyright (C) 2000\-2025 John F. Reiser
.PP
\&\fBUPX\fR is distributed with full source code under the terms of the
GNU General Public License v2+; either under the pure GPLv2+ (see
the file COPYING), or (at your option) under the GPLv+2 with special
exceptions and restrictions granting the free usage for all binaries
including commercial programs (see the file LICENSE).
.PP
This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
.PP
You should have received a copy of the UPX License Agreements along
with this program; see the files COPYING and LICENSE. If not,
visit the UPX home page.
