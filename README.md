# UPX解包工具

这是一个用于解包UPX压缩的可执行文件的工具集合。

## 文件说明

- `upx_unpacker.py` - Python版本的UPX解包工具（功能最全）
- `setup_and_unpack.bat` - Windows批处理脚本（自动安装UPX并解包）
- `upx_unpack.ps1` - PowerShell脚本（推荐使用）
- `GSEEGB.exe` - 待解包的UPX压缩文件

## 使用方法

### 方法1：使用PowerShell脚本（推荐）

```powershell
# 解包GSEEGB.exe（默认）
.\upx_unpack.ps1

# 解包指定文件
.\upx_unpack.ps1 -ExeFile "your_file.exe"

# 指定输出文件名
.\upx_unpack.ps1 -ExeFile "input.exe" -OutputFile "output.exe"

# 强制重新下载UPX
.\upx_unpack.ps1 -DownloadUPX
```

### 方法2：使用批处理脚本

双击运行 `setup_and_unpack.bat` 即可自动完成所有操作。

### 方法3：使用Python脚本

```bash
# 解包GSEEGB.exe
python upx_unpacker.py

# 解包指定文件
python upx_unpacker.py your_file.exe

# 指定输出文件
python upx_unpacker.py input.exe -o output.exe

# 批量解包当前目录所有exe文件
python upx_unpacker.py -b

# 仅检查文件是否为UPX压缩
python upx_unpacker.py your_file.exe -c
```

## 功能特点

1. **自动安装UPX** - 脚本会自动下载并安装UPX工具
2. **智能检测** - 自动检测文件是否为UPX压缩格式
3. **文件大小对比** - 显示压缩前后的文件大小和压缩率
4. **错误处理** - 完善的错误处理和用户提示
5. **批量处理** - 支持批量解包多个文件

## 注意事项

1. 确保有足够的磁盘空间存放解包后的文件
2. 解包后的文件通常比原文件大很多
3. 某些被保护的exe文件可能无法解包
4. 建议在解包前备份原文件

## 故障排除

### 如果PowerShell脚本无法运行：

```powershell
# 设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 如果下载UPX失败：

1. 检查网络连接
2. 手动下载UPX：https://upx.github.io/
3. 将upx.exe放在脚本同目录下

### 如果解包失败：

1. 确认文件确实是UPX压缩的
2. 检查文件是否损坏
3. 尝试使用不同版本的UPX

## UPX简介

UPX (Ultimate Packer for eXecutables) 是一个开源的可执行文件压缩工具，支持多种操作系统和文件格式。它可以显著减小可执行文件的大小，同时保持文件的功能不变。

压缩后的文件在运行时会自动解压到内存中，因此不会影响程序的正常运行。
