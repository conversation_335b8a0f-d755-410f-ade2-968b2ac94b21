# UPX解包PowerShell脚本
# 用于自动下载UPX并解包exe文件

param(
    [string]$ExeFile = "GSEEGB.exe",
    [string]$OutputFile = "",
    [switch]$DownloadUPX = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "UPX解包工具 - PowerShell版本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查UPX是否存在
function Test-UPXExists {
    $upxPath = Get-Command upx -ErrorAction SilentlyContinue
    if ($upxPath) {
        Write-Host "✓ 找到UPX: $($upxPath.Source)" -ForegroundColor Green
        return $upxPath.Source
    }
    
    # 检查当前目录是否有upx.exe
    if (Test-Path ".\upx.exe") {
        Write-Host "✓ 找到本地UPX: .\upx.exe" -ForegroundColor Green
        return ".\upx.exe"
    }
    
    return $null
}

# 下载并安装UPX
function Install-UPX {
    Write-Host "正在下载UPX..." -ForegroundColor Yellow
    
    $upxUrl = "https://github.com/upx/upx/releases/download/v4.2.2/upx-4.2.2-win64.zip"
    $zipFile = "upx.zip"
    $extractDir = "upx_temp"
    
    try {
        # 下载UPX
        Invoke-WebRequest -Uri $upxUrl -OutFile $zipFile -UseBasicParsing
        Write-Host "✓ 下载完成" -ForegroundColor Green
        
        # 解压
        Expand-Archive -Path $zipFile -DestinationPath $extractDir -Force
        
        # 查找upx.exe
        $upxExe = Get-ChildItem -Path $extractDir -Name "upx.exe" -Recurse | Select-Object -First 1
        if ($upxExe) {
            $sourcePath = Join-Path $extractDir $upxExe.DirectoryName "upx.exe"
            Copy-Item $sourcePath ".\upx.exe"
            Write-Host "✓ UPX安装完成" -ForegroundColor Green
        }
        
        # 清理临时文件
        Remove-Item $zipFile -Force -ErrorAction SilentlyContinue
        Remove-Item $extractDir -Recurse -Force -ErrorAction SilentlyContinue
        
        return ".\upx.exe"
    }
    catch {
        Write-Host "✗ 下载UPX失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 检查文件是否为UPX压缩
function Test-UPXPacked {
    param([string]$FilePath, [string]$UPXPath)
    
    try {
        $result = & $UPXPath -t $FilePath 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $FilePath 是UPX压缩文件" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ $FilePath 不是UPX压缩文件" -ForegroundColor Red
            Write-Host "UPX输出: $result" -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host "✗ 检查文件时出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 解包UPX文件
function Invoke-UPXUnpack {
    param([string]$InputFile, [string]$OutputFile, [string]$UPXPath)
    
    if (-not (Test-Path $InputFile)) {
        Write-Host "✗ 文件不存在: $InputFile" -ForegroundColor Red
        return $false
    }
    
    # 检查是否为UPX文件
    if (-not (Test-UPXPacked -FilePath $InputFile -UPXPath $UPXPath)) {
        return $false
    }
    
    # 确定输出文件名
    if ([string]::IsNullOrEmpty($OutputFile)) {
        $baseName = [System.IO.Path]::GetFileNameWithoutExtension($InputFile)
        $extension = [System.IO.Path]::GetExtension($InputFile)
        $OutputFile = "${baseName}_unpacked${extension}"
    }
    
    try {
        # 复制文件
        Copy-Item $InputFile $OutputFile
        Write-Host "已复制文件到: $OutputFile" -ForegroundColor Cyan
        
        # 解包
        Write-Host "正在解包..." -ForegroundColor Yellow
        $result = & $UPXPath -d $OutputFile 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 解包成功!" -ForegroundColor Green
            Write-Host "解包后文件: $OutputFile" -ForegroundColor Green
            
            # 显示文件大小对比
            $originalSize = (Get-Item $InputFile).Length
            $unpackedSize = (Get-Item $OutputFile).Length
            $compressionRatio = (1 - $originalSize / $unpackedSize) * 100
            
            Write-Host "原始文件大小: $($originalSize.ToString('N0')) 字节" -ForegroundColor Cyan
            Write-Host "解包后大小: $($unpackedSize.ToString('N0')) 字节" -ForegroundColor Cyan
            Write-Host "压缩率: $($compressionRatio.ToString('F1'))%" -ForegroundColor Cyan
            
            return $true
        } else {
            Write-Host "✗ 解包失败" -ForegroundColor Red
            Write-Host "错误信息: $result" -ForegroundColor Red
            Remove-Item $OutputFile -Force -ErrorAction SilentlyContinue
            return $false
        }
    }
    catch {
        Write-Host "✗ 解包时出错: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主程序
$upxPath = Test-UPXExists

if (-not $upxPath -or $DownloadUPX) {
    Write-Host "需要安装UPX..." -ForegroundColor Yellow
    $upxPath = Install-UPX
    if (-not $upxPath) {
        Write-Host "无法安装UPX，请手动下载并安装" -ForegroundColor Red
        exit 1
    }
}

# 执行解包
if (Test-Path $ExeFile) {
    Write-Host "开始处理文件: $ExeFile" -ForegroundColor Cyan
    $success = Invoke-UPXUnpack -InputFile $ExeFile -OutputFile $OutputFile -UPXPath $upxPath
    
    if ($success) {
        Write-Host "`n解包完成！" -ForegroundColor Green
    } else {
        Write-Host "`n解包失败！" -ForegroundColor Red
    }
} else {
    Write-Host "✗ 文件不存在: $ExeFile" -ForegroundColor Red
    Write-Host "用法示例:" -ForegroundColor Yellow
    Write-Host "  .\upx_unpack.ps1                    # 解包GSEEGB.exe" -ForegroundColor Gray
    Write-Host "  .\upx_unpack.ps1 -ExeFile test.exe  # 解包指定文件" -ForegroundColor Gray
    Write-Host "  .\upx_unpack.ps1 -DownloadUPX       # 强制重新下载UPX" -ForegroundColor Gray
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
