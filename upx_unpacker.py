#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UPX解包工具脚本
用于解压UPX压缩的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import argparse

class UPXUnpacker:
    def __init__(self):
        self.upx_path = None
        self.check_upx_installation()
    
    def check_upx_installation(self):
        """检查UPX是否已安装"""
        try:
            # 尝试在PATH中找到upx
            self.upx_path = shutil.which('upx')
            if self.upx_path:
                print(f"✓ 找到UPX: {self.upx_path}")
                return True
            else:
                print("✗ 未找到UPX，正在尝试安装...")
                return self.install_upx()
        except Exception as e:
            print(f"检查UPX时出错: {e}")
            return False
    
    def install_upx(self):
        """安装UPX工具"""
        try:
            print("正在下载并安装UPX 5.0.2...")

            # 对于Windows系统，自动下载UPX
            if os.name == 'nt':  # Windows
                import urllib.request
                import zipfile

                upx_url = "https://github.com/upx/upx/releases/download/v5.0.2/upx-5.0.2-win64.zip"
                zip_file = "upx.zip"

                print(f"从 {upx_url} 下载UPX...")
                urllib.request.urlretrieve(upx_url, zip_file)
                print("✓ 下载完成")

                print("正在解压...")
                with zipfile.ZipFile(zip_file, 'r') as zip_ref:
                    zip_ref.extractall("upx_temp")

                # 查找upx.exe
                for root, dirs, files in os.walk("upx_temp"):
                    if "upx.exe" in files:
                        upx_source = os.path.join(root, "upx.exe")
                        shutil.copy2(upx_source, "upx.exe")
                        print("✓ UPX 5.0.2 安装完成")
                        break

                # 清理临时文件
                os.remove(zip_file)
                shutil.rmtree("upx_temp", ignore_errors=True)

                self.upx_path = "./upx.exe" if os.path.exists("upx.exe") else None
                return self.upx_path is not None
            else:
                # Linux/Mac可以尝试包管理器
                subprocess.run(['sudo', 'apt-get', 'install', 'upx-ucl'], check=True)
                self.upx_path = shutil.which('upx')
                return self.upx_path is not None

        except subprocess.CalledProcessError:
            print("自动安装失败，请手动安装UPX")
            return False
        except Exception as e:
            print(f"安装UPX时出错: {e}")
            return False
    
    def check_if_upx_packed(self, exe_path):
        """检查文件是否被UPX压缩"""
        try:
            if not self.upx_path:
                print("UPX未安装，无法检查文件")
                return False
                
            result = subprocess.run(
                [self.upx_path, '-t', exe_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"✓ {exe_path} 是UPX压缩文件")
                return True
            else:
                print(f"✗ {exe_path} 不是UPX压缩文件或已损坏")
                print(f"UPX输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("检查超时")
            return False
        except Exception as e:
            print(f"检查文件时出错: {e}")
            return False
    
    def unpack_exe(self, exe_path, output_path=None):
        """解包UPX压缩的exe文件"""
        try:
            if not os.path.exists(exe_path):
                print(f"错误: 文件 {exe_path} 不存在")
                return False
            
            if not self.upx_path:
                print("错误: UPX未安装")
                return False
            
            # 检查是否为UPX文件
            if not self.check_if_upx_packed(exe_path):
                return False
            
            # 确定输出路径
            if output_path is None:
                base_name = Path(exe_path).stem
                output_path = f"{base_name}_unpacked.exe"
            
            # 复制原文件到输出路径
            shutil.copy2(exe_path, output_path)
            print(f"已复制文件到: {output_path}")
            
            # 执行解包
            print("正在解包...")
            result = subprocess.run(
                [self.upx_path, '-d', output_path],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print(f"✓ 解包成功!")
                print(f"解包后文件: {output_path}")
                
                # 显示文件大小对比
                original_size = os.path.getsize(exe_path)
                unpacked_size = os.path.getsize(output_path)
                compression_ratio = (1 - original_size / unpacked_size) * 100
                
                print(f"原始文件大小: {original_size:,} 字节")
                print(f"解包后大小: {unpacked_size:,} 字节")
                print(f"压缩率: {compression_ratio:.1f}%")
                
                return True
            else:
                print(f"✗ 解包失败")
                print(f"错误信息: {result.stderr}")
                # 删除失败的输出文件
                if os.path.exists(output_path):
                    os.remove(output_path)
                return False
                
        except subprocess.TimeoutExpired:
            print("解包超时")
            return False
        except Exception as e:
            print(f"解包时出错: {e}")
            return False
    
    def batch_unpack(self, directory="."):
        """批量解包目录中的所有exe文件"""
        exe_files = list(Path(directory).glob("*.exe"))
        
        if not exe_files:
            print(f"在目录 {directory} 中未找到exe文件")
            return
        
        print(f"找到 {len(exe_files)} 个exe文件")
        
        for exe_file in exe_files:
            print(f"\n处理文件: {exe_file}")
            self.unpack_exe(str(exe_file))

def main():
    parser = argparse.ArgumentParser(description="UPX解包工具")
    parser.add_argument("exe_file", nargs="?", help="要解包的exe文件路径")
    parser.add_argument("-o", "--output", help="输出文件路径")
    parser.add_argument("-b", "--batch", action="store_true", help="批量处理当前目录的所有exe文件")
    parser.add_argument("-c", "--check", action="store_true", help="仅检查文件是否为UPX压缩")
    
    args = parser.parse_args()
    
    unpacker = UPXUnpacker()
    
    if args.batch:
        unpacker.batch_unpack()
    elif args.exe_file:
        if args.check:
            unpacker.check_if_upx_packed(args.exe_file)
        else:
            unpacker.unpack_exe(args.exe_file, args.output)
    else:
        # 默认处理当前目录的GSEEGB.exe
        if os.path.exists("GSEEGB.exe"):
            print("找到GSEEGB.exe，开始解包...")
            unpacker.unpack_exe("GSEEGB.exe")
        else:
            print("请指定要解包的exe文件")
            parser.print_help()

if __name__ == "__main__":
    main()
