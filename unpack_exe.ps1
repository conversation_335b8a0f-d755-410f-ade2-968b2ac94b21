# UPX解包脚本
# 用于解包UPX压缩的可执行文件

param(
    [Parameter(Mandatory=$false)]
    [string]$ExeFile = "GSEEGB.exe",
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = "unpacked"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "=== UPX 解包工具 ===" -ForegroundColor Green
Write-Host "目标文件: $ExeFile" -ForegroundColor Yellow

# 检查目标文件是否存在
if (-not (Test-Path $ExeFile)) {
    Write-Error "错误: 找不到文件 '$ExeFile'"
    exit 1
}

# 创建输出目录
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    Write-Host "创建输出目录: $OutputDir" -ForegroundColor Green
}

# 检查是否已安装UPX
$upxPath = $null
try {
    $upxPath = Get-Command upx -ErrorAction Stop
    Write-Host "发现已安装的UPX: $($upxPath.Source)" -ForegroundColor Green
} catch {
    Write-Host "未发现UPX工具，开始下载..." -ForegroundColor Yellow
    
    # 下载UPX
    $upxUrl = "https://github.com/upx/upx/releases/download/v4.2.1/upx-4.2.1-win64.zip"
    $upxZip = "upx.zip"
    $upxDir = "upx-tools"
    
    try {
        Write-Host "正在下载UPX工具..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $upxUrl -OutFile $upxZip -UseBasicParsing
        
        Write-Host "正在解压UPX工具..." -ForegroundColor Yellow
        Expand-Archive -Path $upxZip -DestinationPath $upxDir -Force
        
        # 查找upx.exe
        $upxExe = Get-ChildItem -Path $upxDir -Name "upx.exe" -Recurse | Select-Object -First 1
        if ($upxExe) {
            $upxPath = Join-Path $upxDir $upxExe.DirectoryName "upx.exe"
            Write-Host "UPX工具下载完成: $upxPath" -ForegroundColor Green
        } else {
            Write-Error "下载的UPX工具中未找到upx.exe"
            exit 1
        }
        
        # 清理下载文件
        Remove-Item $upxZip -Force
        
    } catch {
        Write-Error "下载UPX工具失败: $($_.Exception.Message)"
        exit 1
    }
}

# 设置UPX命令路径
if ($upxPath -is [System.Management.Automation.CommandInfo]) {
    $upxCommand = $upxPath.Source
} else {
    $upxCommand = $upxPath
}

Write-Host "使用UPX工具: $upxCommand" -ForegroundColor Green

# 复制原文件到输出目录
$originalFile = Join-Path $OutputDir "original_$ExeFile"
Copy-Item $ExeFile $originalFile -Force
Write-Host "已备份原文件到: $originalFile" -ForegroundColor Green

# 执行解包
$unpackedFile = Join-Path $OutputDir "unpacked_$ExeFile"
Copy-Item $ExeFile $unpackedFile -Force

try {
    Write-Host "正在解包文件..." -ForegroundColor Yellow
    & $upxCommand -d $unpackedFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "解包成功!" -ForegroundColor Green
        Write-Host "解包后的文件: $unpackedFile" -ForegroundColor Green
        
        # 显示文件大小对比
        $originalSize = (Get-Item $ExeFile).Length
        $unpackedSize = (Get-Item $unpackedFile).Length
        
        Write-Host "`n=== 文件大小对比 ===" -ForegroundColor Cyan
        Write-Host "原文件大小: $([math]::Round($originalSize/1MB, 2)) MB" -ForegroundColor White
        Write-Host "解包后大小: $([math]::Round($unpackedSize/1MB, 2)) MB" -ForegroundColor White
        Write-Host "压缩比: $([math]::Round((1-$originalSize/$unpackedSize)*100, 1))%" -ForegroundColor White
        
    } else {
        Write-Error "解包失败，退出代码: $LASTEXITCODE"
        exit 1
    }
    
} catch {
    Write-Error "解包过程中发生错误: $($_.Exception.Message)"
    exit 1
}

Write-Host "`n=== 解包完成 ===" -ForegroundColor Green
Write-Host "输出目录: $OutputDir" -ForegroundColor Yellow
Write-Host "- 原文件备份: $originalFile" -ForegroundColor White
Write-Host "- 解包后文件: $unpackedFile" -ForegroundColor White
